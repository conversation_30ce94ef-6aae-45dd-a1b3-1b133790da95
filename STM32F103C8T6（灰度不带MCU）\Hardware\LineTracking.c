#include "stm32f10x.h"
#include "LineTracking.h"
#include "Grayscale.h"

// 寻迹算法参数配置
#define BASE_SPEED 30        // 基础速度百分比
#define KP_FACTOR 3          // 比例控制系数，调节转向强度
#define MAX_TURN_RATE 20     // 最大转向率，限制转向幅度
#define LOST_LINE_TIMEOUT 40 // 丢线保持时间(主循环次数)

// 寻迹状态变量
static int16_t last_error = 0;         // 上次误差值，用于丢线记忆
static uint16_t lost_line_counter = 0; // 丢线计数器
static uint16_t lost_line_timeout = LOST_LINE_TIMEOUT; // 可调节的丢线超时时间

// 寻迹算法初始化，重置所有状态变量
void LineTracking_Init(void)
{
    last_error = 0;
    lost_line_counter = 0;
}

// 计算寻迹误差，基于8路传感器的加权位置算法
int16_t LineTracking_CalculateError(uint8_t sensor_digital)
{
    int16_t error = 0;
    int16_t position = 0;
    uint8_t sensor_count = 0;

    // 计算加权位置，传感器权重：-7,-5,-3,-1,1,3,5,7
    for(int i = 0; i < 8; i++)
    {
        if(sensor_digital & (1 << i))  // 检测到黑线
        {
            position += (i * 2 - 7);   // 位置权重计算
            sensor_count++;
        }
    }

    if(sensor_count > 0)  // 检测到黑线
    {
        error = position / sensor_count;  // 平均位置误差
        last_error = error;               // 保存误差用于丢线记忆
        lost_line_counter = 0;            // 重置丢线计数
    }
    else  // 丢线状态
    {
        if(lost_line_counter < lost_line_timeout)
        {
            error = last_error;           // 保持上次转向方向
            lost_line_counter++;          // 丢线计数递增
        }
        else
        {
            error = 0;                    // 超时后恢复直走
        }
    }

    return error;
}

// 寻迹控制算法，基于误差计算转向率并执行差速控制
void LineTracking_Control(int16_t error)
{
    int16_t turn_rate = error * KP_FACTOR;  // 比例控制计算转向率

    // 限制转向率在最大范围内
    if(turn_rate > MAX_TURN_RATE) turn_rate = MAX_TURN_RATE;
    if(turn_rate < -MAX_TURN_RATE) turn_rate = -MAX_TURN_RATE;

    extern void SetDifferentialTurn(int16_t baseSpeed, int16_t turnRate);
    SetDifferentialTurn(BASE_SPEED, turn_rate);  // 执行差速转向
}

// 寻迹主处理函数，传感器数据处理和控制的入口
void LineTracking_Process(Grayscale_Sensor* sensor)
{
    uint8_t digital = Grayscale_GetDigital(sensor);  // 获取传感器数字状态
    int16_t error = LineTracking_CalculateError(digital);  // 计算寻迹误差
    LineTracking_Control(error);  // 执行寻迹控制
}

// 设置丢线保持时间，用于调节寻迹参数
void LineTracking_SetLostLineTimeout(uint16_t timeout)
{
    lost_line_timeout = timeout;
}
