#include "motor_app.h"

Motor_t right_motor;
Motor_t left_motor; 

void Motor_Init(void)
{
  // 右电机：AIN1=TIM1_CH2, AIN2=TIM1_CH3, 反装
  Motor_Create(&left_motor, &htim1,
               TIM_CHANNEL_1, TIM_CHANNEL_2,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_9, GPIO_AF1_TIM1,       // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_11, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               0);                                     // 反装

  // 左电机：AIN1=TIM1_CH4, AIN2=TIM1_CH1, 正装
  Motor_Create(&right_motor, &htim1,
               TIM_CHANNEL_3, TIM_CHANNEL_4,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_13, GPIO_AF1_TIM1,      // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_14, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               1);                                     // 正装
}

//speed -100.0 ~ +100.0, 支持一位小数精度
void motor_set_l(float speed)
{
	Motor_SetSpeed(&left_motor, speed);
}

void motor_set_r(float speed)
{
	Motor_SetSpeed(&right_motor, speed);
}

void motor_break(void)
{
	Motor_Stop(&right_motor);
	Motor_Stop(&left_motor);
}
