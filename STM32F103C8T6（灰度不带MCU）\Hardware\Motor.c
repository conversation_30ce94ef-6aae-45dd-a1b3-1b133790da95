#include "stm32f10x.h"
#include "PWM.h"
#include "Encoder.h"

// 左电机初始化，配置PB0/PB1为方向控制引脚
void Left_moto_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1; // PB0/PB1控制左电机方向
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
}

// 左电机前进，PB0=1 PB1=0
void Left_moto_go(void)
{
	GPIO_SetBits(GPIOB, GPIO_Pin_0);   // 正转
	GPIO_ResetBits(GPIOB, GPIO_Pin_1);
}

// 左电机后退，PB0=0 PB1=1
void Left_moto_back(void)
{
	GPIO_ResetBits(GPIOB, GPIO_Pin_0); // 反转
	GPIO_SetBits(GPIOB, GPIO_Pin_1);
}

// 左电机停止，PB0=0 PB1=0
void Left_moto_Stop(void)
{
	GPIO_ResetBits(GPIOB, GPIO_Pin_0); // 停止
	GPIO_ResetBits(GPIOB, GPIO_Pin_1);
}

// 双电机系统初始化，配置GPIO、PWM和编码器
void Motor_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_5; // PA4/PA5控制右电机方向
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	Left_moto_Init();   // 初始化左电机GPIO
	PWM_Init();         // 初始化右电机PWM(PA6)
	PWM2_Init();        // 初始化左电机PWM(PB6)
	Encoder2_Init();    // 初始化左电机编码器
}

// 右电机前进，PA4=1 PA5=0
void Right_moto_go(void)
{
	GPIO_SetBits(GPIOA, GPIO_Pin_4);   // 正转
	GPIO_ResetBits(GPIOA, GPIO_Pin_5);
}

// 右电机后退，PA4=0 PA5=1
void Right_moto_back(void)
{
	GPIO_ResetBits(GPIOA, GPIO_Pin_4); // 反转
	GPIO_SetBits(GPIOA, GPIO_Pin_5);
}

// 右电机停止，PA4=0 PA5=0
void Right_moto_Stop(void)
{
	GPIO_ResetBits(GPIOA, GPIO_Pin_4); // 停止
	GPIO_ResetBits(GPIOA, GPIO_Pin_5);
}


