#ifndef __LINETRACKING_H__
#define __LINETRACKING_H__

#include "stm32f10x.h"
#include "Grayscale.h"

// 寻迹算法模块，提供基于8路灰度传感器的智能寻迹功能
// 支持丢线记忆、弧线处理和参数调节

void LineTracking_Init(void);                                    // 寻迹算法初始化
void LineTracking_Process(Grayscale_Sensor* sensor);             // 寻迹主处理函数
int16_t LineTracking_CalculateError(uint8_t sensor_digital);     // 计算寻迹误差
void LineTracking_Control(int16_t error);                        // 寻迹控制算法
void LineTracking_SetLostLineTimeout(uint16_t timeout);          // 设置丢线保持时间

#endif
