Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to main.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for .data
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for .data
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    encoder.o(i.Encoder2_Get) refers to encoder.o(.data) for .data
    encoder.o(i.Encoder2_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(i.Encoder2_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.Encoder2_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.Encoder2_IRQHandler) refers to encoder.o(.data) for .data
    encoder.o(i.Encoder2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder2_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    encoder.o(i.Encoder2_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    encoder.o(i.Encoder2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    encoder.o(i.Encoder2_Init) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.Encoder2_Init) refers to encoder.o(.data) for .data
    encoder.o(i.Encoder_Get) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    encoder.o(i.Encoder_Get) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ICStructInit) for TIM_ICStructInit
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ICInit) for TIM_ICInit
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm.o(i.PWM2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(i.PWM2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    pwm.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OCStructInit) for TIM_OCStructInit
    pwm.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    pwm.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OCStructInit) for TIM_OCStructInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_SetCompare1) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    pwm.o(i.PWM_SetCompare2) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    timer.o(i.Timer_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.Timer_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    timer.o(i.Timer_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    motor.o(i.Left_moto_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.Left_moto_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.Left_moto_Stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Left_moto_back) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Left_moto_back) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Left_moto_go) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Left_moto_go) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.Motor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.Motor_Init) refers to motor.o(i.Left_moto_Init) for Left_moto_Init
    motor.o(i.Motor_Init) refers to pwm.o(i.PWM_Init) for PWM_Init
    motor.o(i.Motor_Init) refers to pwm.o(i.PWM2_Init) for PWM2_Init
    motor.o(i.Motor_Init) refers to encoder.o(i.Encoder2_Init) for Encoder2_Init
    motor.o(i.Right_moto_Stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Right_moto_back) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Right_moto_back) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Right_moto_go) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Right_moto_go) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motorrun.o(i.differential_turn) refers to motorrun.o(i.dual_run) for dual_run
    motorrun.o(i.dual_backrun) refers to pwm.o(i.PWM_SetCompare2) for PWM_SetCompare2
    motorrun.o(i.dual_backrun) refers to pwm.o(i.PWM_SetCompare1) for PWM_SetCompare1
    motorrun.o(i.dual_backrun) refers to motor.o(i.Left_moto_back) for Left_moto_back
    motorrun.o(i.dual_backrun) refers to motor.o(i.Right_moto_back) for Right_moto_back
    motorrun.o(i.dual_run) refers to pwm.o(i.PWM_SetCompare2) for PWM_SetCompare2
    motorrun.o(i.dual_run) refers to pwm.o(i.PWM_SetCompare1) for PWM_SetCompare1
    motorrun.o(i.dual_run) refers to motor.o(i.Left_moto_go) for Left_moto_go
    motorrun.o(i.dual_run) refers to motor.o(i.Right_moto_go) for Right_moto_go
    motorrun.o(i.dual_stop) refers to motor.o(i.Left_moto_Stop) for Left_moto_Stop
    motorrun.o(i.dual_stop) refers to motor.o(i.Right_moto_Stop) for Right_moto_Stop
    adc.o(i.ADC_Config) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.ADC_Config) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.ADC_Config) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.ADC_Config) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.ADC_Config) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.ADC_Config) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adc.o(i.ADC_Config) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adc.o(i.ADC_Config) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adc.o(i.ADC_Config) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    adc.o(i.ADC_GetValue) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    adc.o(i.ADC_GetValue) refers to stm32f10x_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    adc.o(i.ADC_GetValue) refers to stm32f10x_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    grayscale.o(i.Get_Analog_value) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    grayscale.o(i.Get_Analog_value) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    grayscale.o(i.Get_Analog_value) refers to adc.o(i.ADC_GetValue) for ADC_GetValue
    grayscale.o(i.Grayscale_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    grayscale.o(i.Grayscale_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    grayscale.o(i.Grayscale_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    grayscale.o(i.Grayscale_ReadAll) refers to grayscale.o(i.Get_Analog_value) for Get_Analog_value
    grayscale.o(i.Grayscale_ReadAll) refers to grayscale.o(i.convertAnalogToDigital) for convertAnalogToDigital
    grayscale.o(i.Grayscale_ReadAll) refers to grayscale.o(i.normalizeAnalogValues) for normalizeAnalogValues
    grayscale.o(i.Grayscale_Sensor_Init_First) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    grayscale.o(i.Grayscale_Sensor_Init_First) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    grayscale.o(i.Grayscale_Sensor_Init_First) refers to grayscale.o(.constdata) for .constdata
    grayscale.o(i.normalizeAnalogValues) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    grayscale.o(i.normalizeAnalogValues) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    grayscale.o(i.normalizeAnalogValues) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    grayscale.o(i.normalizeAnalogValues) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    grayscale.o(i.normalizeAnalogValues) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    linetracking.o(i.LineTracking_CalculateError) refers to linetracking.o(.data) for .data
    linetracking.o(i.LineTracking_Control) refers to main.o(i.SetDifferentialTurn) for SetDifferentialTurn
    linetracking.o(i.LineTracking_Init) refers to linetracking.o(.data) for .data
    linetracking.o(i.LineTracking_Process) refers to grayscale.o(i.Grayscale_GetDigital) for Grayscale_GetDigital
    linetracking.o(i.LineTracking_Process) refers to linetracking.o(i.LineTracking_CalculateError) for LineTracking_CalculateError
    linetracking.o(i.LineTracking_Process) refers to linetracking.o(i.LineTracking_Control) for LineTracking_Control
    linetracking.o(i.LineTracking_SetLostLineTimeout) refers to linetracking.o(.data) for .data
    main.o(i.SetDifferentialTurn) refers to main.o(.data) for .data
    main.o(i.SetMotorSpeed) refers to main.o(.data) for .data
    main.o(i.StopMotors) refers to main.o(.data) for .data
    main.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    main.o(i.TIM2_IRQHandler) refers to encoder.o(i.Encoder_Get) for Encoder_Get
    main.o(i.TIM2_IRQHandler) refers to encoder.o(i.Encoder2_Get) for Encoder2_Get
    main.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    main.o(i.TIM2_IRQHandler) refers to main.o(.data) for .data
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to timer.o(i.Timer_Init) for Timer_Init
    main.o(i.main) refers to motor.o(i.Motor_Init) for Motor_Init
    main.o(i.main) refers to encoder.o(i.Encoder_Init) for Encoder_Init
    main.o(i.main) refers to adc.o(i.ADC_Config) for ADC_Config
    main.o(i.main) refers to grayscale.o(i.Grayscale_Init) for Grayscale_Init
    main.o(i.main) refers to grayscale.o(i.Grayscale_Sensor_Init_First) for Grayscale_Sensor_Init_First
    main.o(i.main) refers to linetracking.o(i.LineTracking_Init) for LineTracking_Init
    main.o(i.main) refers to motorrun.o(i.differential_turn) for differential_turn
    main.o(i.main) refers to motorrun.o(i.dual_run) for dual_run
    main.o(i.main) refers to motorrun.o(i.dual_backrun) for dual_backrun
    main.o(i.main) refers to motorrun.o(i.dual_stop) for dual_stop
    main.o(i.main) refers to grayscale.o(i.Grayscale_ReadAll) for Grayscale_ReadAll
    main.o(i.main) refers to linetracking.o(i.LineTracking_Process) for LineTracking_Process
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    main.o(i.main) refers to grayscale.o(i.Grayscale_GetDigital) for Grayscale_GetDigital
    main.o(i.main) refers to oled.o(i.OLED_ShowBinNum) for OLED_ShowBinNum
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to main.o(.data) for .data
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to encoder.o(i.Encoder2_IRQHandler) for Encoder2_IRQHandler
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (124 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (28 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (100 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (32 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (18 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (20 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (42 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (136 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (24 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (24 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (212 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (82 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (204 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (20 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (260 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (154 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (130 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (96 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (182 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (136 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (28 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (28 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (28 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (28 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (26 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (248 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (36 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (36 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (20 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (58 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (136 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (60 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (36 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (92 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (136 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (96 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (42 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (18 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (86 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (104 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (36 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (202 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (98 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (36 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (104 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (216 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (28 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (92 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (50 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (34 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (14 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (192 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (16 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (64 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (172 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (56 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (40 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (48 bytes).
    Removing stm32f10x_rcc.o(.data), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (40 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (168 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (26 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (100 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (44 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (56 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (528 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (136 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (132 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (104 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (110 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (76 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (48 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_Cmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (164 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (64 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (54 bytes).
    Removing stm32f10x_usart.o(i.USART_Init), (188 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (32 bytes).
    Removing delay.o(i.Delay_ms), (24 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing delay.o(i.Delay_us), (32 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (80 bytes).
    Removing oled.o(i.OLED_ShowNum), (64 bytes).
    Removing grayscale.o(i.Grayscale_GetAnalog), (2 bytes).
    Removing linetracking.o(i.LineTracking_SetLostLineTimeout), (12 bytes).
    Removing main.o(i.SetMotorSpeed), (20 bytes).
    Removing main.o(i.StopMotors), (20 bytes).

439 unused section(s) (total 14592 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    Hardware\ADC.c                           0x00000000   Number         0  adc.o ABSOLUTE
    Hardware\Encoder.c                       0x00000000   Number         0  encoder.o ABSOLUTE
    Hardware\Grayscale.c                     0x00000000   Number         0  grayscale.o ABSOLUTE
    Hardware\LineTracking.c                  0x00000000   Number         0  linetracking.o ABSOLUTE
    Hardware\Motor.c                         0x00000000   Number         0  motor.o ABSOLUTE
    Hardware\MotorRun.c                      0x00000000   Number         0  motorrun.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\PWM.c                           0x00000000   Number         0  pwm.o ABSOLUTE
    Hardware\Timer.c                         0x00000000   Number         0  timer.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000188   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001c8   Section        0  heapauxi.o(.text)
    .text                                    0x080001ce   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000218   Section        0  exit.o(.text)
    .text                                    0x0800022c   Section        8  libspace.o(.text)
    .text                                    0x08000234   Section        0  sys_exit.o(.text)
    .text                                    0x08000240   Section        2  use_no_semi.o(.text)
    .text                                    0x08000242   Section        0  indicate_semi.o(.text)
    i.ADC_Cmd                                0x08000242   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_Config                             0x0800025c   Section        0  adc.o(i.ADC_Config)
    i.ADC_GetCalibrationStatus               0x080002ec   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetConversionValue                 0x080002fa   Section        0  stm32f10x_adc.o(i.ADC_GetConversionValue)
    i.ADC_GetFlagStatus                      0x08000300   Section        0  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    i.ADC_GetResetCalibrationStatus          0x0800030e   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_GetValue                           0x0800031c   Section        0  adc.o(i.ADC_GetValue)
    i.ADC_Init                               0x08000344   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x0800038c   Section        0  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetCalibration                   0x08000400   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_SoftwareStartConvCmd               0x0800040a   Section        0  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    i.ADC_StartCalibration                   0x08000422   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.BusFault_Handler                       0x0800042c   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x0800042e   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.EXTI0_IRQHandler                       0x08000430   Section        0  stm32f10x_it.o(i.EXTI0_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x08000434   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x08000440   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x08000460   Section        0  stm32f10x_exti.o(i.EXTI_Init)
    i.Encoder2_Get                           0x080004d4   Section        0  encoder.o(i.Encoder2_Get)
    i.Encoder2_IRQHandler                    0x080004e8   Section        0  encoder.o(i.Encoder2_IRQHandler)
    i.Encoder2_Init                          0x08000564   Section        0  encoder.o(i.Encoder2_Init)
    i.Encoder_Get                            0x080005ec   Section        0  encoder.o(i.Encoder_Get)
    i.Encoder_Init                           0x08000608   Section        0  encoder.o(i.Encoder_Init)
    i.GPIO_EXTILineConfig                    0x080006a8   Section        0  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    i.GPIO_Init                              0x080006d0   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x08000776   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000784   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000788   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x0800078c   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.Get_Analog_value                       0x08000798   Section        0  grayscale.o(i.Get_Analog_value)
    i.Grayscale_GetDigital                   0x08000820   Section        0  grayscale.o(i.Grayscale_GetDigital)
    i.Grayscale_Init                         0x08000828   Section        0  grayscale.o(i.Grayscale_Init)
    i.Grayscale_ReadAll                      0x08000860   Section        0  grayscale.o(i.Grayscale_ReadAll)
    i.Grayscale_Sensor_Init_First            0x08000898   Section        0  grayscale.o(i.Grayscale_Sensor_Init_First)
    i.HardFault_Handler                      0x08000938   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Left_moto_Init                         0x0800093c   Section        0  motor.o(i.Left_moto_Init)
    i.Left_moto_Stop                         0x08000964   Section        0  motor.o(i.Left_moto_Stop)
    i.Left_moto_back                         0x08000980   Section        0  motor.o(i.Left_moto_back)
    i.Left_moto_go                           0x0800099c   Section        0  motor.o(i.Left_moto_go)
    i.LineTracking_CalculateError            0x080009b8   Section        0  linetracking.o(i.LineTracking_CalculateError)
    i.LineTracking_Control                   0x08000a08   Section        0  linetracking.o(i.LineTracking_Control)
    i.LineTracking_Init                      0x08000a24   Section        0  linetracking.o(i.LineTracking_Init)
    i.LineTracking_Process                   0x08000a34   Section        0  linetracking.o(i.LineTracking_Process)
    i.MemManage_Handler                      0x08000a46   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.Motor_Init                             0x08000a48   Section        0  motor.o(i.Motor_Init)
    i.NMI_Handler                            0x08000a84   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000a88   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08000aec   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x08000b00   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x08000b28   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08000b78   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08000bd4   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08000c0c   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x08000c3c   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x08000ce8   Section        0  oled.o(i.OLED_Pow)
    i.OLED_SetCursor                         0x08000cf8   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowBinNum                        0x08000d1a   Section        0  oled.o(i.OLED_ShowBinNum)
    i.OLED_ShowChar                          0x08000d54   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowSignedNum                     0x08000dac   Section        0  oled.o(i.OLED_ShowSignedNum)
    i.OLED_ShowString                        0x08000e0e   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08000e34   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08000e56   Section        0  oled.o(i.OLED_WriteData)
    i.PWM2_Init                              0x08000e78   Section        0  pwm.o(i.PWM2_Init)
    i.PWM_Init                               0x08000f08   Section        0  pwm.o(i.PWM_Init)
    i.PWM_SetCompare1                        0x08000f98   Section        0  pwm.o(i.PWM_SetCompare1)
    i.PWM_SetCompare2                        0x08000fa4   Section        0  pwm.o(i.PWM_SetCompare2)
    i.PendSV_Handler                         0x08000fb0   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08000fb4   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000fd0   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.Right_moto_Stop                        0x08000fec   Section        0  motor.o(i.Right_moto_Stop)
    i.Right_moto_back                        0x08001008   Section        0  motor.o(i.Right_moto_back)
    i.Right_moto_go                          0x08001024   Section        0  motor.o(i.Right_moto_go)
    i.SVC_Handler                            0x08001040   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetDifferentialTurn                    0x08001044   Section        0  main.o(i.SetDifferentialTurn)
    i.SetSysClock                            0x08001058   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001059   Thumb Code     4  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x0800105c   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x0800105d   Thumb Code   166  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x0800110c   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001110   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TI1_Config                             0x08001160   Section        0  stm32f10x_tim.o(i.TI1_Config)
    TI1_Config                               0x08001161   Thumb Code    94  stm32f10x_tim.o(i.TI1_Config)
    i.TI2_Config                             0x080011d4   Section        0  stm32f10x_tim.o(i.TI2_Config)
    TI2_Config                               0x080011d5   Thumb Code   102  stm32f10x_tim.o(i.TI2_Config)
    i.TI3_Config                             0x08001250   Section        0  stm32f10x_tim.o(i.TI3_Config)
    TI3_Config                               0x08001251   Thumb Code    98  stm32f10x_tim.o(i.TI3_Config)
    i.TI4_Config                             0x080012c8   Section        0  stm32f10x_tim.o(i.TI4_Config)
    TI4_Config                               0x080012c9   Thumb Code   102  stm32f10x_tim.o(i.TI4_Config)
    i.TIM2_IRQHandler                        0x08001344   Section        0  main.o(i.TIM2_IRQHandler)
    i.TIM_ClearFlag                          0x080013d0   Section        0  stm32f10x_tim.o(i.TIM_ClearFlag)
    i.TIM_ClearITPendingBit                  0x080013d6   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x080013dc   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_EncoderInterfaceConfig             0x080013f4   Section        0  stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig)
    i.TIM_GetCounter                         0x08001426   Section        0  stm32f10x_tim.o(i.TIM_GetCounter)
    i.TIM_GetITStatus                        0x0800142a   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ICInit                             0x08001444   Section        0  stm32f10x_tim.o(i.TIM_ICInit)
    i.TIM_ICStructInit                       0x080014d0   Section        0  stm32f10x_tim.o(i.TIM_ICStructInit)
    i.TIM_ITConfig                           0x080014e0   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_InternalClockConfig                0x080014f4   Section        0  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    i.TIM_OC1Init                            0x08001500   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_OCStructInit                       0x08001588   Section        0  stm32f10x_tim.o(i.TIM_OCStructInit)
    i.TIM_SetCompare1                        0x0800159c   Section        0  stm32f10x_tim.o(i.TIM_SetCompare1)
    i.TIM_SetCounter                         0x080015a0   Section        0  stm32f10x_tim.o(i.TIM_SetCounter)
    i.TIM_SetIC1Prescaler                    0x080015a4   Section        0  stm32f10x_tim.o(i.TIM_SetIC1Prescaler)
    i.TIM_SetIC2Prescaler                    0x080015b4   Section        0  stm32f10x_tim.o(i.TIM_SetIC2Prescaler)
    i.TIM_SetIC3Prescaler                    0x080015cc   Section        0  stm32f10x_tim.o(i.TIM_SetIC3Prescaler)
    i.TIM_SetIC4Prescaler                    0x080015dc   Section        0  stm32f10x_tim.o(i.TIM_SetIC4Prescaler)
    i.TIM_TimeBaseInit                       0x080015f4   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.Timer_Init                             0x08001690   Section        0  timer.o(i.Timer_Init)
    i.UsageFault_Handler                     0x0800170a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.convertAnalogToDigital                 0x0800170c   Section        0  grayscale.o(i.convertAnalogToDigital)
    i.differential_turn                      0x08001768   Section        0  motorrun.o(i.differential_turn)
    i.dual_backrun                           0x08001790   Section        0  motorrun.o(i.dual_backrun)
    i.dual_run                               0x080017c4   Section        0  motorrun.o(i.dual_run)
    i.dual_stop                              0x080017f8   Section        0  motorrun.o(i.dual_stop)
    i.main                                   0x08001808   Section        0  main.o(i.main)
    i.normalizeAnalogValues                  0x08001978   Section        0  grayscale.o(i.normalizeAnalogValues)
    x$fpl$dcmpinf                            0x080019de   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x080019f8   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080019ff   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08001ca8   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x08001d02   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x08001d30   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08001d58   Section      120  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08001dd0   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08001f24   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08001fc0   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08001fcc   Section      108  drleqf.o(x$fpl$drleqf)
    .constdata                               0x08002038   Section     1520  oled.o(.constdata)
    x$fpl$usenofp                            0x08002038   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08002628   Section       32  grayscale.o(.constdata)
    .data                                    0x20000000   Section       12  encoder.o(.data)
    Encoder2_Last_A                          0x20000000   Data           1  encoder.o(.data)
    Encoder2_Last_B                          0x20000001   Data           1  encoder.o(.data)
    Encoder2_Count                           0x20000002   Data           2  encoder.o(.data)
    Encoder2_Last_Time                       0x20000004   Data           4  encoder.o(.data)
    debounce_counter                         0x20000008   Data           4  encoder.o(.data)
    .data                                    0x2000000c   Section        6  linetracking.o(.data)
    last_error                               0x2000000c   Data           2  linetracking.o(.data)
    lost_line_counter                        0x2000000e   Data           2  linetracking.o(.data)
    lost_line_timeout                        0x20000010   Data           2  linetracking.o(.data)
    .data                                    0x20000012   Section       18  main.o(.data)
    .bss                                     0x20000028   Section      176  main.o(.bss)
    .bss                                     0x200000d8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000138   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x20000138   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x20000338   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x20000338   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000738   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000189   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART1_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001a5   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __use_two_region_memory                  0x080001c9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080001cb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080001cd   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x080001cf   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000219   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x0800022d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800022d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800022d   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08000235   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000241   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000241   Thumb Code     2  use_no_semi.o(.text)
    ADC_Cmd                                  0x08000243   Thumb Code    24  stm32f10x_adc.o(i.ADC_Cmd)
    __semihosting_library_function           0x08000243   Thumb Code     0  indicate_semi.o(.text)
    ADC_Config                               0x0800025d   Thumb Code   136  adc.o(i.ADC_Config)
    ADC_GetCalibrationStatus                 0x080002ed   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetConversionValue                   0x080002fb   Thumb Code     6  stm32f10x_adc.o(i.ADC_GetConversionValue)
    ADC_GetFlagStatus                        0x08000301   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    ADC_GetResetCalibrationStatus            0x0800030f   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_GetValue                             0x0800031d   Thumb Code    34  adc.o(i.ADC_GetValue)
    ADC_Init                                 0x08000345   Thumb Code    62  stm32f10x_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x0800038d   Thumb Code   116  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetCalibration                     0x08000401   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_SoftwareStartConvCmd                 0x0800040b   Thumb Code    24  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    ADC_StartCalibration                     0x08000423   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    BusFault_Handler                         0x0800042d   Thumb Code     2  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x0800042f   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    EXTI0_IRQHandler                         0x08000431   Thumb Code     4  stm32f10x_it.o(i.EXTI0_IRQHandler)
    EXTI_ClearITPendingBit                   0x08000435   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x08000441   Thumb Code    28  stm32f10x_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x08000461   Thumb Code   110  stm32f10x_exti.o(i.EXTI_Init)
    Encoder2_Get                             0x080004d5   Thumb Code    16  encoder.o(i.Encoder2_Get)
    Encoder2_IRQHandler                      0x080004e9   Thumb Code   116  encoder.o(i.Encoder2_IRQHandler)
    Encoder2_Init                            0x08000565   Thumb Code   128  encoder.o(i.Encoder2_Init)
    Encoder_Get                              0x080005ed   Thumb Code    24  encoder.o(i.Encoder_Get)
    Encoder_Init                             0x08000609   Thumb Code   150  encoder.o(i.Encoder_Init)
    GPIO_EXTILineConfig                      0x080006a9   Thumb Code    34  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    GPIO_Init                                0x080006d1   Thumb Code   166  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08000777   Thumb Code    14  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000785   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000789   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x0800078d   Thumb Code    12  stm32f10x_gpio.o(i.GPIO_WriteBit)
    Get_Analog_value                         0x08000799   Thumb Code   132  grayscale.o(i.Get_Analog_value)
    Grayscale_GetDigital                     0x08000821   Thumb Code     6  grayscale.o(i.Grayscale_GetDigital)
    Grayscale_Init                           0x08000829   Thumb Code    50  grayscale.o(i.Grayscale_Init)
    Grayscale_ReadAll                        0x08000861   Thumb Code    56  grayscale.o(i.Grayscale_ReadAll)
    Grayscale_Sensor_Init_First              0x08000899   Thumb Code   146  grayscale.o(i.Grayscale_Sensor_Init_First)
    HardFault_Handler                        0x08000939   Thumb Code     2  stm32f10x_it.o(i.HardFault_Handler)
    Left_moto_Init                           0x0800093d   Thumb Code    36  motor.o(i.Left_moto_Init)
    Left_moto_Stop                           0x08000965   Thumb Code    24  motor.o(i.Left_moto_Stop)
    Left_moto_back                           0x08000981   Thumb Code    24  motor.o(i.Left_moto_back)
    Left_moto_go                             0x0800099d   Thumb Code    24  motor.o(i.Left_moto_go)
    LineTracking_CalculateError              0x080009b9   Thumb Code    76  linetracking.o(i.LineTracking_CalculateError)
    LineTracking_Control                     0x08000a09   Thumb Code    28  linetracking.o(i.LineTracking_Control)
    LineTracking_Init                        0x08000a25   Thumb Code    10  linetracking.o(i.LineTracking_Init)
    LineTracking_Process                     0x08000a35   Thumb Code    18  linetracking.o(i.LineTracking_Process)
    MemManage_Handler                        0x08000a47   Thumb Code     2  stm32f10x_it.o(i.MemManage_Handler)
    Motor_Init                               0x08000a49   Thumb Code    54  motor.o(i.Motor_Init)
    NMI_Handler                              0x08000a85   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000a89   Thumb Code    96  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08000aed   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x08000b01   Thumb Code    38  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x08000b29   Thumb Code    76  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08000b79   Thumb Code    86  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08000bd5   Thumb Code    52  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08000c0d   Thumb Code    42  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x08000c3d   Thumb Code   172  oled.o(i.OLED_Init)
    OLED_Pow                                 0x08000ce9   Thumb Code    16  oled.o(i.OLED_Pow)
    OLED_SetCursor                           0x08000cf9   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowBinNum                          0x08000d1b   Thumb Code    58  oled.o(i.OLED_ShowBinNum)
    OLED_ShowChar                            0x08000d55   Thumb Code    82  oled.o(i.OLED_ShowChar)
    OLED_ShowSignedNum                       0x08000dad   Thumb Code    98  oled.o(i.OLED_ShowSignedNum)
    OLED_ShowString                          0x08000e0f   Thumb Code    38  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08000e35   Thumb Code    34  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08000e57   Thumb Code    34  oled.o(i.OLED_WriteData)
    PWM2_Init                                0x08000e79   Thumb Code   136  pwm.o(i.PWM2_Init)
    PWM_Init                                 0x08000f09   Thumb Code   136  pwm.o(i.PWM_Init)
    PWM_SetCompare1                          0x08000f99   Thumb Code     8  pwm.o(i.PWM_SetCompare1)
    PWM_SetCompare2                          0x08000fa5   Thumb Code     8  pwm.o(i.PWM_SetCompare2)
    PendSV_Handler                           0x08000fb1   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000fb5   Thumb Code    22  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000fd1   Thumb Code    22  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    Right_moto_Stop                          0x08000fed   Thumb Code    24  motor.o(i.Right_moto_Stop)
    Right_moto_back                          0x08001009   Thumb Code    24  motor.o(i.Right_moto_back)
    Right_moto_go                            0x08001025   Thumb Code    24  motor.o(i.Right_moto_go)
    SVC_Handler                              0x08001041   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SetDifferentialTurn                      0x08001045   Thumb Code    14  main.o(i.SetDifferentialTurn)
    SysTick_Handler                          0x0800110d   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08001111   Thumb Code    64  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08001345   Thumb Code   134  main.o(i.TIM2_IRQHandler)
    TIM_ClearFlag                            0x080013d1   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearFlag)
    TIM_ClearITPendingBit                    0x080013d7   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x080013dd   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_EncoderInterfaceConfig               0x080013f5   Thumb Code    50  stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig)
    TIM_GetCounter                           0x08001427   Thumb Code     4  stm32f10x_tim.o(i.TIM_GetCounter)
    TIM_GetITStatus                          0x0800142b   Thumb Code    24  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ICInit                               0x08001445   Thumb Code   122  stm32f10x_tim.o(i.TIM_ICInit)
    TIM_ICStructInit                         0x080014d1   Thumb Code    16  stm32f10x_tim.o(i.TIM_ICStructInit)
    TIM_ITConfig                             0x080014e1   Thumb Code    20  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_InternalClockConfig                  0x080014f5   Thumb Code    10  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    TIM_OC1Init                              0x08001501   Thumb Code   114  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_OCStructInit                         0x08001589   Thumb Code    20  stm32f10x_tim.o(i.TIM_OCStructInit)
    TIM_SetCompare1                          0x0800159d   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare1)
    TIM_SetCounter                           0x080015a1   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCounter)
    TIM_SetIC1Prescaler                      0x080015a5   Thumb Code    16  stm32f10x_tim.o(i.TIM_SetIC1Prescaler)
    TIM_SetIC2Prescaler                      0x080015b5   Thumb Code    24  stm32f10x_tim.o(i.TIM_SetIC2Prescaler)
    TIM_SetIC3Prescaler                      0x080015cd   Thumb Code    16  stm32f10x_tim.o(i.TIM_SetIC3Prescaler)
    TIM_SetIC4Prescaler                      0x080015dd   Thumb Code    24  stm32f10x_tim.o(i.TIM_SetIC4Prescaler)
    TIM_TimeBaseInit                         0x080015f5   Thumb Code   114  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    Timer_Init                               0x08001691   Thumb Code   122  timer.o(i.Timer_Init)
    UsageFault_Handler                       0x0800170b   Thumb Code     2  stm32f10x_it.o(i.UsageFault_Handler)
    convertAnalogToDigital                   0x0800170d   Thumb Code    92  grayscale.o(i.convertAnalogToDigital)
    differential_turn                        0x08001769   Thumb Code    40  motorrun.o(i.differential_turn)
    dual_backrun                             0x08001791   Thumb Code    52  motorrun.o(i.dual_backrun)
    dual_run                                 0x080017c5   Thumb Code    52  motorrun.o(i.dual_run)
    dual_stop                                0x080017f9   Thumb Code    14  motorrun.o(i.dual_stop)
    main                                     0x08001809   Thumb Code   308  main.o(i.main)
    normalizeAnalogValues                    0x08001979   Thumb Code   102  grayscale.o(i.normalizeAnalogValues)
    __fpl_dcmp_Inf                           0x080019df   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x080019f9   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080019f9   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08001ca9   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08001ca9   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x08001d03   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08001d03   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x08001d31   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08001d31   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08001d59   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08001d59   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08001dbb   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08001dd1   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08001dd1   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08001f25   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08001fc1   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08001fcd   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08001fcd   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    OLED_F8x16                               0x08002038   Data        1520  oled.o(.constdata)
    __I$use$fp                               0x08002038   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08002648   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002668   Number         0  anon$$obj.o(Region$$Table)
    SpeedControlActive                       0x20000012   Data           1  main.o(.data)
    system_ready                             0x20000013   Data           1  main.o(.data)
    TargetSpeed                              0x20000014   Data           2  main.o(.data)
    LeftTargetSpeed                          0x20000016   Data           2  main.o(.data)
    TurnRate                                 0x20000018   Data           2  main.o(.data)
    LeftSpeedPercent                         0x2000001a   Data           2  main.o(.data)
    RightSpeedPercent                        0x2000001c   Data           2  main.o(.data)
    startup_delay_counter                    0x2000001e   Data           2  main.o(.data)
    Speed                                    0x20000020   Data           2  main.o(.data)
    LeftSpeed                                0x20000022   Data           2  main.o(.data)
    grayscale_sensor                         0x20000028   Data         176  main.o(.bss)
    __libspace_start                         0x200000d8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000138   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000268c, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002668, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         3847  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         4033    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         4035    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         4037    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000002   Code   RO         3901    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3908    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3910    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3913    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3915    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3917    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3920    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3922    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3924    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3926    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3928    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3930    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3932    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3934    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3936    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3938    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3940    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3944    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3946    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3948    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         3950    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000002   Code   RO         3951    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x08000164   0x00000002   Code   RO         3971    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x08000166   0x00000000   Code   RO         3984    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         3986    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         3988    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         3991    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         3994    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         3996    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         3999    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000002   Code   RO         4000    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000168   0x08000168   0x00000000   Code   RO         3867    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x08000168   0x00000000   Code   RO         3876    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x08000168   0x00000006   Code   RO         3888    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x0800016e   0x00000000   Code   RO         3878    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x0800016e   0x00000004   Code   RO         3879    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         3881    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000008   Code   RO         3882    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x0800017a   0x00000002   Code   RO         3905    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3953    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x0800017c   0x00000004   Code   RO         3954    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x08000180   0x00000006   Code   RO         3955    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x00000040   Code   RO            4    .text               startup_stm32f10x_md.o
    0x080001c8   0x080001c8   0x00000006   Code   RO         3845    .text               c_w.l(heapauxi.o)
    0x080001ce   0x080001ce   0x0000004a   Code   RO         3892    .text               c_w.l(sys_stackheap_outer.o)
    0x08000218   0x08000218   0x00000012   Code   RO         3894    .text               c_w.l(exit.o)
    0x0800022a   0x0800022a   0x00000002   PAD
    0x0800022c   0x0800022c   0x00000008   Code   RO         3902    .text               c_w.l(libspace.o)
    0x08000234   0x08000234   0x0000000c   Code   RO         3963    .text               c_w.l(sys_exit.o)
    0x08000240   0x08000240   0x00000002   Code   RO         3974    .text               c_w.l(use_no_semi.o)
    0x08000242   0x08000242   0x00000000   Code   RO         3976    .text               c_w.l(indicate_semi.o)
    0x08000242   0x08000242   0x00000018   Code   RO          202    i.ADC_Cmd           stm32f10x_adc.o
    0x0800025a   0x0800025a   0x00000002   PAD
    0x0800025c   0x0800025c   0x00000090   Code   RO         3584    i.ADC_Config        adc.o
    0x080002ec   0x080002ec   0x0000000e   Code   RO          210    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x080002fa   0x080002fa   0x00000006   Code   RO          211    i.ADC_GetConversionValue  stm32f10x_adc.o
    0x08000300   0x08000300   0x0000000e   Code   RO          213    i.ADC_GetFlagStatus  stm32f10x_adc.o
    0x0800030e   0x0800030e   0x0000000e   Code   RO          216    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x0800031c   0x0800031c   0x00000028   Code   RO         3585    i.ADC_GetValue      adc.o
    0x08000344   0x08000344   0x00000048   Code   RO          220    i.ADC_Init          stm32f10x_adc.o
    0x0800038c   0x0800038c   0x00000074   Code   RO          224    i.ADC_RegularChannelConfig  stm32f10x_adc.o
    0x08000400   0x08000400   0x0000000a   Code   RO          225    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x0800040a   0x0800040a   0x00000018   Code   RO          227    i.ADC_SoftwareStartConvCmd  stm32f10x_adc.o
    0x08000422   0x08000422   0x0000000a   Code   RO          229    i.ADC_StartCalibration  stm32f10x_adc.o
    0x0800042c   0x0800042c   0x00000002   Code   RO         3771    i.BusFault_Handler  stm32f10x_it.o
    0x0800042e   0x0800042e   0x00000002   Code   RO         3772    i.DebugMon_Handler  stm32f10x_it.o
    0x08000430   0x08000430   0x00000004   Code   RO         3773    i.EXTI0_IRQHandler  stm32f10x_it.o
    0x08000434   0x08000434   0x0000000c   Code   RO          985    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08000440   0x08000440   0x00000020   Code   RO          989    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x08000460   0x08000460   0x00000074   Code   RO          990    i.EXTI_Init         stm32f10x_exti.o
    0x080004d4   0x080004d4   0x00000014   Code   RO         3328    i.Encoder2_Get      encoder.o
    0x080004e8   0x080004e8   0x0000007c   Code   RO         3329    i.Encoder2_IRQHandler  encoder.o
    0x08000564   0x08000564   0x00000088   Code   RO         3330    i.Encoder2_Init     encoder.o
    0x080005ec   0x080005ec   0x0000001c   Code   RO         3331    i.Encoder_Get       encoder.o
    0x08000608   0x08000608   0x000000a0   Code   RO         3332    i.Encoder_Init      encoder.o
    0x080006a8   0x080006a8   0x00000028   Code   RO         1344    i.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x080006d0   0x080006d0   0x000000a6   Code   RO         1347    i.GPIO_Init         stm32f10x_gpio.o
    0x08000776   0x08000776   0x0000000e   Code   RO         1351    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000784   0x08000784   0x00000004   Code   RO         1354    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000788   0x08000788   0x00000004   Code   RO         1355    i.GPIO_SetBits      stm32f10x_gpio.o
    0x0800078c   0x0800078c   0x0000000c   Code   RO         1358    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08000798   0x08000798   0x00000088   Code   RO         3602    i.Get_Analog_value  grayscale.o
    0x08000820   0x08000820   0x00000006   Code   RO         3604    i.Grayscale_GetDigital  grayscale.o
    0x08000826   0x08000826   0x00000002   PAD
    0x08000828   0x08000828   0x00000038   Code   RO         3605    i.Grayscale_Init    grayscale.o
    0x08000860   0x08000860   0x00000038   Code   RO         3606    i.Grayscale_ReadAll  grayscale.o
    0x08000898   0x08000898   0x000000a0   Code   RO         3607    i.Grayscale_Sensor_Init_First  grayscale.o
    0x08000938   0x08000938   0x00000002   Code   RO         3774    i.HardFault_Handler  stm32f10x_it.o
    0x0800093a   0x0800093a   0x00000002   PAD
    0x0800093c   0x0800093c   0x00000028   Code   RO         3408    i.Left_moto_Init    motor.o
    0x08000964   0x08000964   0x0000001c   Code   RO         3409    i.Left_moto_Stop    motor.o
    0x08000980   0x08000980   0x0000001c   Code   RO         3410    i.Left_moto_back    motor.o
    0x0800099c   0x0800099c   0x0000001c   Code   RO         3411    i.Left_moto_go      motor.o
    0x080009b8   0x080009b8   0x00000050   Code   RO         3663    i.LineTracking_CalculateError  linetracking.o
    0x08000a08   0x08000a08   0x0000001c   Code   RO         3664    i.LineTracking_Control  linetracking.o
    0x08000a24   0x08000a24   0x00000010   Code   RO         3665    i.LineTracking_Init  linetracking.o
    0x08000a34   0x08000a34   0x00000012   Code   RO         3666    i.LineTracking_Process  linetracking.o
    0x08000a46   0x08000a46   0x00000002   Code   RO         3775    i.MemManage_Handler  stm32f10x_it.o
    0x08000a48   0x08000a48   0x0000003c   Code   RO         3412    i.Motor_Init        motor.o
    0x08000a84   0x08000a84   0x00000002   Code   RO         3776    i.NMI_Handler       stm32f10x_it.o
    0x08000a86   0x08000a86   0x00000002   PAD
    0x08000a88   0x08000a88   0x00000064   Code   RO          137    i.NVIC_Init         misc.o
    0x08000aec   0x08000aec   0x00000014   Code   RO          138    i.NVIC_PriorityGroupConfig  misc.o
    0x08000b00   0x08000b00   0x00000026   Code   RO         3220    i.OLED_Clear        oled.o
    0x08000b26   0x08000b26   0x00000002   PAD
    0x08000b28   0x08000b28   0x00000050   Code   RO         3221    i.OLED_I2C_Init     oled.o
    0x08000b78   0x08000b78   0x0000005c   Code   RO         3222    i.OLED_I2C_SendByte  oled.o
    0x08000bd4   0x08000bd4   0x00000038   Code   RO         3223    i.OLED_I2C_Start    oled.o
    0x08000c0c   0x08000c0c   0x00000030   Code   RO         3224    i.OLED_I2C_Stop     oled.o
    0x08000c3c   0x08000c3c   0x000000ac   Code   RO         3225    i.OLED_Init         oled.o
    0x08000ce8   0x08000ce8   0x00000010   Code   RO         3226    i.OLED_Pow          oled.o
    0x08000cf8   0x08000cf8   0x00000022   Code   RO         3227    i.OLED_SetCursor    oled.o
    0x08000d1a   0x08000d1a   0x0000003a   Code   RO         3228    i.OLED_ShowBinNum   oled.o
    0x08000d54   0x08000d54   0x00000058   Code   RO         3229    i.OLED_ShowChar     oled.o
    0x08000dac   0x08000dac   0x00000062   Code   RO         3232    i.OLED_ShowSignedNum  oled.o
    0x08000e0e   0x08000e0e   0x00000026   Code   RO         3233    i.OLED_ShowString   oled.o
    0x08000e34   0x08000e34   0x00000022   Code   RO         3234    i.OLED_WriteCommand  oled.o
    0x08000e56   0x08000e56   0x00000022   Code   RO         3235    i.OLED_WriteData    oled.o
    0x08000e78   0x08000e78   0x00000090   Code   RO         3366    i.PWM2_Init         pwm.o
    0x08000f08   0x08000f08   0x00000090   Code   RO         3367    i.PWM_Init          pwm.o
    0x08000f98   0x08000f98   0x0000000c   Code   RO         3368    i.PWM_SetCompare1   pwm.o
    0x08000fa4   0x08000fa4   0x0000000c   Code   RO         3369    i.PWM_SetCompare2   pwm.o
    0x08000fb0   0x08000fb0   0x00000002   Code   RO         3777    i.PendSV_Handler    stm32f10x_it.o
    0x08000fb2   0x08000fb2   0x00000002   PAD
    0x08000fb4   0x08000fb4   0x0000001c   Code   RO         1775    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000fd0   0x08000fd0   0x0000001c   Code   RO         1777    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000fec   0x08000fec   0x0000001c   Code   RO         3413    i.Right_moto_Stop   motor.o
    0x08001008   0x08001008   0x0000001c   Code   RO         3414    i.Right_moto_back   motor.o
    0x08001024   0x08001024   0x0000001c   Code   RO         3415    i.Right_moto_go     motor.o
    0x08001040   0x08001040   0x00000002   Code   RO         3778    i.SVC_Handler       stm32f10x_it.o
    0x08001042   0x08001042   0x00000002   PAD
    0x08001044   0x08001044   0x00000014   Code   RO         3707    i.SetDifferentialTurn  main.o
    0x08001058   0x08001058   0x00000004   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x0800105c   0x0800105c   0x000000b0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x0800110c   0x0800110c   0x00000002   Code   RO         3779    i.SysTick_Handler   stm32f10x_it.o
    0x0800110e   0x0800110e   0x00000002   PAD
    0x08001110   0x08001110   0x00000050   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x08001160   0x08001160   0x00000074   Code   RO         2405    i.TI1_Config        stm32f10x_tim.o
    0x080011d4   0x080011d4   0x0000007c   Code   RO         2406    i.TI2_Config        stm32f10x_tim.o
    0x08001250   0x08001250   0x00000078   Code   RO         2407    i.TI3_Config        stm32f10x_tim.o
    0x080012c8   0x080012c8   0x0000007c   Code   RO         2408    i.TI4_Config        stm32f10x_tim.o
    0x08001344   0x08001344   0x0000008c   Code   RO         3710    i.TIM2_IRQHandler   main.o
    0x080013d0   0x080013d0   0x00000006   Code   RO         2415    i.TIM_ClearFlag     stm32f10x_tim.o
    0x080013d6   0x080013d6   0x00000006   Code   RO         2416    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x080013dc   0x080013dc   0x00000018   Code   RO         2421    i.TIM_Cmd           stm32f10x_tim.o
    0x080013f4   0x080013f4   0x00000032   Code   RO         2430    i.TIM_EncoderInterfaceConfig  stm32f10x_tim.o
    0x08001426   0x08001426   0x00000004   Code   RO         2440    i.TIM_GetCounter    stm32f10x_tim.o
    0x0800142a   0x0800142a   0x00000018   Code   RO         2442    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08001442   0x08001442   0x00000002   PAD
    0x08001444   0x08001444   0x0000008c   Code   RO         2444    i.TIM_ICInit        stm32f10x_tim.o
    0x080014d0   0x080014d0   0x00000010   Code   RO         2445    i.TIM_ICStructInit  stm32f10x_tim.o
    0x080014e0   0x080014e0   0x00000014   Code   RO         2446    i.TIM_ITConfig      stm32f10x_tim.o
    0x080014f4   0x080014f4   0x0000000a   Code   RO         2448    i.TIM_InternalClockConfig  stm32f10x_tim.o
    0x080014fe   0x080014fe   0x00000002   PAD
    0x08001500   0x08001500   0x00000088   Code   RO         2450    i.TIM_OC1Init       stm32f10x_tim.o
    0x08001588   0x08001588   0x00000014   Code   RO         2468    i.TIM_OCStructInit  stm32f10x_tim.o
    0x0800159c   0x0800159c   0x00000004   Code   RO         2482    i.TIM_SetCompare1   stm32f10x_tim.o
    0x080015a0   0x080015a0   0x00000004   Code   RO         2486    i.TIM_SetCounter    stm32f10x_tim.o
    0x080015a4   0x080015a4   0x00000010   Code   RO         2487    i.TIM_SetIC1Prescaler  stm32f10x_tim.o
    0x080015b4   0x080015b4   0x00000018   Code   RO         2488    i.TIM_SetIC2Prescaler  stm32f10x_tim.o
    0x080015cc   0x080015cc   0x00000010   Code   RO         2489    i.TIM_SetIC3Prescaler  stm32f10x_tim.o
    0x080015dc   0x080015dc   0x00000018   Code   RO         2490    i.TIM_SetIC4Prescaler  stm32f10x_tim.o
    0x080015f4   0x080015f4   0x0000009c   Code   RO         2492    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001690   0x08001690   0x0000007a   Code   RO         3396    i.Timer_Init        timer.o
    0x0800170a   0x0800170a   0x00000002   Code   RO         3780    i.UsageFault_Handler  stm32f10x_it.o
    0x0800170c   0x0800170c   0x0000005c   Code   RO         3608    i.convertAnalogToDigital  grayscale.o
    0x08001768   0x08001768   0x00000028   Code   RO         3551    i.differential_turn  motorrun.o
    0x08001790   0x08001790   0x00000034   Code   RO         3552    i.dual_backrun      motorrun.o
    0x080017c4   0x080017c4   0x00000034   Code   RO         3553    i.dual_run          motorrun.o
    0x080017f8   0x080017f8   0x0000000e   Code   RO         3554    i.dual_stop         motorrun.o
    0x08001806   0x08001806   0x00000002   PAD
    0x08001808   0x08001808   0x00000170   Code   RO         3711    i.main              main.o
    0x08001978   0x08001978   0x00000066   Code   RO         3609    i.normalizeAnalogValues  grayscale.o
    0x080019de   0x080019de   0x00000018   Code   RO         3890    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x080019f6   0x080019f6   0x00000002   PAD
    0x080019f8   0x080019f8   0x000002b0   Code   RO         3850    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08001ca8   0x08001ca8   0x0000005a   Code   RO         3853    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x08001d02   0x08001d02   0x0000002e   Code   RO         3858    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x08001d30   0x08001d30   0x00000026   Code   RO         3857    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x08001d56   0x08001d56   0x00000002   PAD
    0x08001d58   0x08001d58   0x00000078   Code   RO         3868    x$fpl$dleqf         fz_ws.l(dleqf.o)
    0x08001dd0   0x08001dd0   0x00000154   Code   RO         3863    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08001f24   0x08001f24   0x0000009c   Code   RO         3870    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08001fc0   0x08001fc0   0x0000000c   Code   RO         3872    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08001fcc   0x08001fcc   0x0000006c   Code   RO         3865    x$fpl$drleqf        fz_ws.l(drleqf.o)
    0x08002038   0x08002038   0x00000000   Code   RO         3874    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08002038   0x08002038   0x000005f0   Data   RO         3236    .constdata          oled.o
    0x08002628   0x08002628   0x00000020   Data   RO         3610    .constdata          grayscale.o
    0x08002648   0x08002648   0x00000020   Data   RO         4031    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002668, Size: 0x00000738, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002668   0x0000000c   Data   RW         3333    .data               encoder.o
    0x2000000c   0x08002674   0x00000006   Data   RW         3668    .data               linetracking.o
    0x20000012   0x0800267a   0x00000012   Data   RW         3713    .data               main.o
    0x20000024   0x0800268c   0x00000004   PAD
    0x20000028        -       0x000000b0   Zero   RW         3712    .bss                main.o
    0x200000d8        -       0x00000060   Zero   RW         3903    .bss                c_w.l(libspace.o)
    0x20000138        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f10x_md.o
    0x20000338        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       184         14          0          0          0       1080   adc.o
         0          0          0          0          0       4516   core_cm3.o
       468         34          0         12          0       3526   encoder.o
       608         24         32          0          0       5986   grayscale.o
       142         10          0          6          0       3632   linetracking.o
       528         72          0         18        176       3152   main.o
       120         14          0          0          0     216035   misc.o
       268         34          0          0          0      11983   motor.o
       158          0          0          0          0       2158   motorrun.o
       886         26       1520          0          0       8545   oled.o
       312         24          0          0          0       2163   pwm.o
        64         26        236          0       1536        808   startup_stm32f10x_md.o
       304         10          0          0          0      18717   stm32f10x_adc.o
       160         16          0          0          0       4292   stm32f10x_exti.o
       240          6          0          0          0      13110   stm32f10x_gpio.o
        22          0          0          0          0       4233   stm32f10x_it.o
        56         12          0          0          0       1130   stm32f10x_rcc.o
      1184        170          0          0          0      35254   stm32f10x_tim.o
       260         26          0          0          0      30517   system_stm32f10x.o
       122          0          0          0          0        561   timer.o

    ----------------------------------------------------------------------
      6108        <USER>       <GROUP>         36       1716     371398   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        24          0          0          0          0         68   dcmpi.o
       688        140          0          0          0        208   ddiv.o
        90          4          0          0          0         92   dfixu.o
        84          0          0          0          0        136   dflt_clz.o
       120          4          0          0          0         92   dleqf.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       108          0          0          0          0         80   drleqf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      1904        <USER>          <GROUP>          0         96       1524   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       272         16          0          0         96        584   c_w.l
      1622        164          0          0          0        940   fz_ws.l

    ----------------------------------------------------------------------
      1904        <USER>          <GROUP>          0         96       1524   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      8012        698       1820         36       1812     366730   Grand Totals
      8012        698       1820         36       1812     366730   ELF Image Totals
      8012        698       1820         36          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9832 (   9.60kB)
    Total RW  Size (RW Data + ZI Data)              1848 (   1.80kB)
    Total ROM Size (Code + RO Data + RW Data)       9868 (   9.64kB)

==============================================================================

